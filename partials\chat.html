<div class="page-header">
    <h2>聊天</h2>
    <button class="group-chat-btn" id="group-chat-btn" title="创建群聊">
        <i class="fas fa-users"></i>
    </button>
</div>
<div class="chat-list" id="chat-list">
    <!-- 聊天列表将通过JavaScript动态生成 -->
</div>

<!-- 移动端聊天操作提示 -->
<div class="mobile-chat-tips" id="mobile-chat-tips" style="display: none;">
    <div class="tips-content">
        <i class="fas fa-hand-point-left"></i>
        <span>向左滑动聊天可删除</span>
    </div>
</div>

<!-- 聊天详情页面 -->
<div class="chat-detail" id="chat-detail">
    <div class="chat-header">
        <button id="back-to-chat" class="back-btn">
            <i class="fas fa-arrow-left"></i>
        </button>
        <div class="chat-title-container">
            <h3 id="chat-detail-name">联系人名称</h3>
            <span id="chat-member-count" class="member-count" style="display: none;"></span>
        </div>
        <button class="more-btn" id="chat-more-btn">
            <i class="fas fa-ellipsis-h"></i>
        </button>
    </div>
    <div class="chat-messages" id="chat-messages">
        <!-- 聊天消息将通过JavaScript动态生成 -->
    </div>

    <!-- 未读消息提示条 -->
    <div class="unread-indicator" id="unread-indicator" style="display: none;">
        <div class="unread-indicator-content">
            <span class="unread-count" id="unread-count">0</span>
            <span class="unread-text">条未读消息</span>
        </div>
        <button class="unread-scroll-btn" id="unread-scroll-btn">
            <i class="fas fa-arrow-down"></i>
        </button>
    </div>

    <div class="chat-input-area">
        <!-- 功能按钮区域 -->
        <div class="chat-function-bar">
            <div class="function-btn emoji-btn" id="emoji-btn" title="表情">
                <i class="fas fa-smile"></i>
            </div>
            <div class="function-btn media-btn" id="media-btn" title="图片/视频">
                <i class="fas fa-image"></i>
                <input type="file" id="media-upload" accept="image/*,video/*" style="display: none">
            </div>
            <div class="function-btn file-btn" id="file-btn" title="文件">
                <i class="fas fa-paperclip"></i>
                <input type="file" id="file-upload" style="display: none">
            </div>
        </div>

        <!-- 输入框和发送按钮 -->
        <div class="chat-input-container">
            <div class="input-wrapper">
                <textarea id="chat-input" placeholder="发送消息..." rows="1"></textarea>
            </div>
            <button id="send-button" class="send-button">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>

        <!-- 表情选择器 -->
        <div id="emoji-picker" class="emoji-picker">
            <div class="emoji-picker-header">
                <div class="emoji-categories">
                    <button class="emoji-category active" data-category="recent">
                        <i class="fas fa-clock"></i>
                    </button>
                    <button class="emoji-category" data-category="smileys">
                        <i class="fas fa-smile"></i>
                    </button>
                    <button class="emoji-category" data-category="people">
                        <i class="fas fa-user"></i>
                    </button>
                    <button class="emoji-category" data-category="nature">
                        <i class="fas fa-leaf"></i>
                    </button>
                    <button class="emoji-category" data-category="food">
                        <i class="fas fa-apple-alt"></i>
                    </button>
                    <button class="emoji-category" data-category="activities">
                        <i class="fas fa-futbol"></i>
                    </button>
                    <button class="emoji-category" data-category="travel">
                        <i class="fas fa-car"></i>
                    </button>
                    <button class="emoji-category" data-category="objects">
                        <i class="fas fa-lightbulb"></i>
                    </button>
                </div>
            </div>
            <div class="emoji-picker-content">
                <!-- emoji 列表将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 评论栏 -->
    <div class="comment-bar" style="display: none;">
        <textarea id="comment-content" placeholder="评论"></textarea>
        <div class="comment-bar-actions">
            <button id="comment-emoji-btn" class="emoji-btn">😊</button>
            <button id="submit-comment" class="send-btn" disabled>发送</button>
            <button id="cancel-comment" class="cancel-btn">取消</button>
        </div>
        <div id="comment-emoji-picker" class="emoji-picker">
            <div class="emoji-container">
                <!-- emoji 列表 -->
            </div>
        </div>
    </div>
</div>

<!-- 创建群聊页面 - 大厂卖萌风格 -->
<div class="create-group-chat" id="create-group-chat">
    <div class="group-chat-header">
        <button id="back-to-chat-list" class="back-btn cute-tooltip" data-tooltip="返回聊天列表">
            <i class="fas fa-arrow-left"></i>
        </button>
        <h3>创建群聊</h3>
        <button id="create-group-confirm" class="confirm-btn cute-tooltip" data-tooltip="创建新群聊" disabled>
            <span class="btn-text">完成</span>
            <span class="btn-loading" style="display: none;">
                <span class="cute-loading"></span>
            </span>
        </button>
    </div>

    <div class="group-info-section">
        <div class="group-avatar-container">
            <div class="group-avatar-preview" id="group-avatar-preview">
                <i class="fas fa-users"></i>
                <div class="avatar-glow"></div>
            </div>
            <input type="file" id="group-avatar-upload" accept="image/*" style="display: none">
            <button class="change-avatar-btn cute-tooltip" id="change-avatar-btn" data-tooltip="更换群头像">
                <i class="fas fa-camera"></i>
            </button>
        </div>
        <div class="group-name-input-container">
            <input type="text" id="group-name-input" placeholder="给你的群起个可爱的名字吧~ ✨" maxlength="20">
            <span class="input-counter">0/20</span>
        </div>
        <div class="input-tips">
            <span class="tip-icon">💡</span>
            <span class="tip-text">好的群名能让大家更容易记住哦！</span>
        </div>
    </div>

    <div class="member-selection-section">
        <div class="section-header">
            <h4>选择成员</h4>
            <span class="selected-count" id="selected-count">已选择 0 人</span>
        </div>
        <div class="selected-members" id="selected-members">
            <div class="empty-selection-hint">
                <span class="hint-emoji">👋</span>
                <span class="hint-text">点击下方好友来邀请他们加入群聊</span>
            </div>
        </div>
        <div class="friends-list-container">
            <div class="search-container">
                <input type="text" id="member-search" placeholder="搜索好友姓名...">
                <i class="fas fa-search"></i>
            </div>
            <div class="friends-list" id="create-group-friends-list">
                <!-- 好友列表通过JavaScript动态生成 -->
                <div class="loading-placeholder" style="display: none;">
                    <div class="loading-item">
                        <div class="loading-avatar"></div>
                        <div class="loading-info">
                            <div class="loading-name"></div>
                            <div class="loading-status"></div>
                        </div>
                    </div>
                    <div class="loading-item">
                        <div class="loading-avatar"></div>
                        <div class="loading-info">
                            <div class="loading-name"></div>
                            <div class="loading-status"></div>
                        </div>
                    </div>
                    <div class="loading-item">
                        <div class="loading-avatar"></div>
                        <div class="loading-info">
                            <div class="loading-name"></div>
                            <div class="loading-status"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动装饰元素 -->
    <div class="floating-decorations">
        <div class="decoration-item decoration-1">✨</div>
        <div class="decoration-item decoration-2">💫</div>
        <div class="decoration-item decoration-3">⭐</div>
        <div class="decoration-item decoration-4">🌟</div>
    </div>
</div>

<!-- 群聊详情页面 -->
<div class="group-chat-detail" id="group-chat-detail">
    <div class="group-detail-header">
        <button id="back-to-group-chat" class="back-btn">
            <i class="fas fa-arrow-left"></i>
        </button>
        <h3 id="group-detail-name">群聊详情</h3>
        <button class="group-more-btn" id="group-more-btn">
            <i class="fas fa-ellipsis-h"></i>
        </button>
    </div>
    
    <div class="group-info-card">
        <div class="group-avatar-large" id="group-avatar-large">
            <i class="fas fa-users"></i>
        </div>
        <div class="group-basic-info">
            <h4 id="group-name-display">群聊名称</h4>
            <span class="group-member-count" id="group-member-count">0人</span>
        </div>
    </div>
    
    <div class="group-members-section">
        <div class="section-title">
            <h5>群成员</h5>
            <button class="add-member-btn" id="add-member-btn">
                <i class="fas fa-plus"></i>
            </button>
        </div>
        <div class="group-members-grid" id="group-members-grid">
            <!-- 群成员网格通过JavaScript动态生成 -->
        </div>
    </div>
    
    <div class="group-actions-section">
        <div class="action-item" id="group-notifications">
            <div class="action-icon">
                <i class="fas fa-bell"></i>
            </div>
            <div class="action-content">
                <span class="action-title">消息免打扰</span>
                <div class="toggle-switch">
                    <input type="checkbox" id="notification-toggle">
                    <label for="notification-toggle"></label>
                </div>
            </div>
        </div>
        
        <div class="action-item" id="group-top">
            <div class="action-icon">
                <i class="fas fa-thumbtack"></i>
            </div>
            <div class="action-content">
                <span class="action-title">置顶聊天</span>
                <div class="toggle-switch">
                    <input type="checkbox" id="top-toggle">
                    <label for="top-toggle"></label>
                </div>
            </div>
        </div>
        
        <div class="action-item danger-action" id="leave-group">
            <div class="action-icon">
                <i class="fas fa-sign-out-alt"></i>
            </div>
            <div class="action-content">
                <span class="action-title">退出群聊</span>
                <i class="fas fa-chevron-right"></i>
            </div>
        </div>
        
        <div class="action-item danger-action group-owner-only" id="dissolve-group" style="display: none;">
            <div class="action-icon">
                <i class="fas fa-trash-alt"></i>
            </div>
            <div class="action-content">
                <span class="action-title">解散群聊</span>
                <i class="fas fa-chevron-right"></i>
            </div>
        </div>
    </div>
</div>

<!-- 添加成员页面 -->
<div class="add-member-page" id="add-member-page">
    <div class="add-member-header">
        <button id="back-to-group-detail" class="back-btn">
            <i class="fas fa-arrow-left"></i>
        </button>
        <h3>添加群成员</h3>
        <button class="confirm-add-btn" id="confirm-add-btn" disabled>
            <span>确定</span>
            <span class="selected-count">(0)</span>
        </button>
    </div>

    <div class="add-member-content">
        <!-- 搜索框 -->
        <div class="member-search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="add-member-search" placeholder="搜索好友" class="search-input">
                <button class="clear-search-btn" id="clear-add-search" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- 已选择的成员 -->
        <div class="selected-members-section" id="selected-add-members-section" style="display: none;">
            <div class="section-label">已选择</div>
            <div class="selected-members-container" id="selected-add-members-container">
                <!-- 已选择的成员将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 好友列表 -->
        <div class="friends-list-section">
            <div class="section-label">选择好友</div>
            <div class="friends-list" id="add-member-friends-list">
                <!-- 好友列表将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
</div>